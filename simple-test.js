const axios = require('axios');

const testLogin = async () => {
  try {
    console.log('Testing login...');
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'Pa$$w0rd!'
    });
    
    console.log('Login successful!');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    return response.data.data.token;
  } catch (error) {
    console.log('Login failed!');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
};

const testKioskAPI = async (token) => {
  try {
    console.log('\nTesting kiosk device settings...');
    const response = await axios.get('http://localhost:3001/api/kiosk/device/KIOSK_LOBBY_001/setting', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('Kiosk API successful!');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Kiosk API failed!');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
  }
};

const runTest = async () => {
  const token = await testLogin();
  if (token) {
    await testKioskAPI(token);
  }
};

runTest();
