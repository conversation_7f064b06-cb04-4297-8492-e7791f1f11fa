const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';
let authToken = '';

// Test credentials
const credentials = {
  email: "<EMAIL>",
  password: "Pa$$w0rd!"
};

// Helper function to make authenticated requests
const makeRequest = async (method, url, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    };
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
};

// Test functions
const testLogin = async () => {
  console.log('\n🔐 Testing Login...');
  const result = await makeRequest('POST', '/auth/login', credentials);
  
  if (result.success) {
    authToken = result.data.data.token;
    console.log('✅ Login successful');
    console.log('📝 Token received:', authToken.substring(0, 20) + '...');
  } else {
    console.log('❌ Login failed:', result.error);
  }
  
  return result.success;
};

const testKioskDeviceSettings = async () => {
  console.log('\n🏥 Testing Kiosk Device Settings...');
  
  // Test with valid device identifier
  const validResult = await makeRequest('GET', '/kiosk/device/KIOSK_LOBBY_001/setting');
  
  if (validResult.success) {
    console.log('✅ Device settings retrieved successfully');
    console.log('📊 Settings:', JSON.stringify(validResult.data.data, null, 2));
  } else {
    console.log('❌ Failed to get device settings:', validResult.error);
  }
  
  // Test with invalid device identifier
  console.log('\n🔍 Testing with invalid device identifier...');
  const invalidResult = await makeRequest('GET', '/kiosk/device/INVALID_DEVICE/setting');
  
  if (!invalidResult.success && invalidResult.status === 404) {
    console.log('✅ Proper error handling for invalid device');
    console.log('📝 Error message:', invalidResult.error.message);
  } else {
    console.log('❌ Error handling not working properly for invalid device');
  }
};

const testKioskDeviceTemplate = async () => {
  console.log('\n📄 Testing Kiosk Device Template...');
  
  // First, get a valid NDA template ID from device settings
  const deviceResult = await makeRequest('GET', '/kiosk/device/KIOSK_LOBBY_001/setting');
  
  if (deviceResult.success && deviceResult.data.data.nda_template) {
    const ndaTemplateId = deviceResult.data.data.nda_template.nda_template_id;
    console.log('🔍 Using NDA template ID:', ndaTemplateId);
    
    const templateResult = await makeRequest('GET', `/kiosk/device/${ndaTemplateId}/templates`);
    
    if (templateResult.success) {
      console.log('✅ NDA template retrieved successfully');
      console.log('📄 Template:', JSON.stringify(templateResult.data.data, null, 2));
    } else {
      console.log('❌ Failed to get NDA template:', templateResult.error);
    }
    
    // Test with invalid template ID
    console.log('\n🔍 Testing with invalid template ID...');
    const invalidTemplateResult = await makeRequest('GET', '/kiosk/device/invalid-uuid/templates');
    
    if (!invalidTemplateResult.success) {
      console.log('✅ Proper error handling for invalid template ID');
      console.log('📝 Error message:', invalidTemplateResult.error.message);
    } else {
      console.log('❌ Error handling not working properly for invalid template ID');
    }
  } else {
    console.log('❌ Could not get NDA template ID from device settings');
  }
};

const testDeviceRoutes = async () => {
  console.log('\n📱 Testing Device Routes...');

  const facilityId = "53c2ea2e-7664-4552-a3e8-419166d247b7"; // From seeder data

  // Test getting all devices for a facility
  const devicesResult = await makeRequest('GET', `/facility/devices/${facilityId}`);

  if (devicesResult.success) {
    console.log('✅ Devices retrieved successfully');
    console.log('📱 Number of devices:', devicesResult.data.data.length);
    console.log('📝 First device:', JSON.stringify(devicesResult.data.data[0], null, 2));
  } else {
    console.log('❌ Failed to get devices:', devicesResult.error);
  }

  // Test creating a new device
  console.log('\n➕ Testing device creation...');
  const newDevice = {
    name: "Test Kiosk",
    identifier: "KIOSK_TEST_001",
    kiosk_group_id: "7b417c80-d1ac-4ed3-ba02-b38ce4f7653d", // Main Lobby Kiosks
    facility_id: facilityId,
    building_id: "de1c2dae-2b39-4a74-aad0-7b356818a319",
    floor_id: "bb73e8bf-5741-4070-94cb-0aeef5cb0ab0",
    room_id: "be3733ea-0dc2-43a3-9aa0-3d14deaaaf16"
  };

  const createResult = await makeRequest('POST', `/facility/devices/${facilityId}`, newDevice);

  if (createResult.success) {
    console.log('✅ Device created successfully');
    console.log('📱 New device:', JSON.stringify(createResult.data.data, null, 2));
  } else {
    console.log('❌ Failed to create device:', createResult.error);
  }

  // Test with invalid data
  console.log('\n🔍 Testing device creation with invalid data...');
  const invalidDevice = {
    name: "", // Invalid: empty name
    identifier: "KIOSK_TEST_002"
  };

  const invalidCreateResult = await makeRequest('POST', `/facility/devices/${facilityId}`, invalidDevice);

  if (!invalidCreateResult.success && invalidCreateResult.status === 400) {
    console.log('✅ Proper validation error handling');
    console.log('📝 Validation errors:', invalidCreateResult.error.message);
  } else {
    console.log('❌ Validation error handling not working properly');
  }
};

// Main test runner
const runTests = async () => {
  console.log('🚀 Starting API Tests...');
  
  const loginSuccess = await testLogin();
  
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }
  
  await testKioskDeviceSettings();
  await testKioskDeviceTemplate();
  await testDeviceRoutes();
  
  console.log('\n✅ All tests completed!');
};

// Run the tests
runTests().catch(console.error);
